// parseProtocolHeader 测试文件
// 测试 IPv4、Domain name、IPv6 地址解析功能

// 模拟全局配置
const globalControllerConfig = {
    targetProtocolType0: '18c-_-1b0-_-1bc-_-1d4-_-190-_-198-_-1b0-_-184-_-1c8-_-194-_-e8-_-1d8-_-1b0-_-194-_-1cc-_-1cc', // VLESS
    targetProtocolType1: '18c-_-1b0-_-1bc-_-1d4-_-190-_-198-_-1b0-_-184-_-1c8-_-194-_-e8-_-1d0-_-1c8-_-1bc-_-1a8-_-184-_-1b8'  // Trojan
};

const globalSessionConfig = {
    user: {
        id: '49f37b98-c37f-4d46-be93-1fe0a742dd43'
    }
};

// UUID 匹配函数
function matchUuid(extractedID, uuidString) {
    uuidString = uuidString.replaceAll('-', '');
    for (let index = 0; index < 16; index++) {
        const expected = parseInt(uuidString.substring(index * 2, index * 2 + 2), 16);
        if (extractedID[index] !== expected) {
            return false;
        }
    }
    return true;
}

// parseProtocolHeader 函数 (从 _worker.js 复制)
function parseProtocolHeader(buffer, protocolMode, wsInterface = null) {
    if (!buffer || buffer.byteLength === 0) throw new Error('Invalid buffer');

    const bytes = new Uint8Array(buffer);
    const view = new DataView(buffer);
    const decoder = new TextDecoder();

    if (protocolMode === globalControllerConfig.targetProtocolType0) {
        // VLESS 协议解析
        if (buffer.byteLength < 24) {
            throw new Error('Invalid VLESS buffer');
        }

        // UUID 验证 (需要 Uint8Array)
        const extractedID = bytes.subarray(1, 17);
        if (!matchUuid(extractedID, globalSessionConfig.user.id)) {
            if (wsInterface && wsInterface.close instanceof Function) {
                wsInterface.close(1013, 'Invalid user');
            }
            throw new Error('Invalid user: UUID does not match');
        }

        const version = view.getUint8(0);
        const optLength = view.getUint8(17);
        const commandIndex = 18 + optLength;
        const command = view.getUint8(commandIndex); // 0x01 TCP, 0x02 UDP, 0x03 MUX
        const portIndex = 18 + optLength + 1;
        const port = view.getUint16(portIndex); // port is big-Endian in raw data etc 80 == 0x005d

        let addressIndex = portIndex + 2;
        const addressType = view.getUint8(addressIndex);
        let hostname = '';
        let offset = addressIndex + 1; // 从 addressType 之后开始使用 offset

        // 地址解析
        switch (addressType) {
            case 1: // IPv4
                if (offset + 4 > bytes.length) throw new Error('Invalid IPv4 address');
                hostname = `${bytes[offset]}.${bytes[offset + 1]}.${bytes[offset + 2]}.${bytes[offset + 3]}`;
                offset += 4;
                break;
            case 2: // Domain name
                if (offset >= bytes.length) throw new Error('Invalid domain length');
                const domainLength = bytes[offset++];
                if (offset + domainLength > bytes.length) throw new Error('Invalid domain');
                hostname = decoder.decode(bytes.subarray(offset, offset + domainLength));
                offset += domainLength;
                break;
            case 3: // IPv6
                if (offset + 16 > bytes.length) throw new Error('Invalid IPv6 address');
                const ipv6Parts = [];
                for (let i = 0; i < 16; i += 2) {
                    ipv6Parts.push(((bytes[offset + i] << 8) | bytes[offset + i + 1]).toString(16));
                }
                hostname = ipv6Parts.join(':');
                offset += 16;
                break;
            default:
                throw new Error(`Unsupported address type: ${addressType}`);
        }

        const rawClientData = bytes.subarray(offset);

        return {
            addressType,
            addressRemote: hostname,
            portRemote: port,
            rawClientData,
            version
        };
    } else if (protocolMode === globalControllerConfig.targetProtocolType1) {
        // Trojan 协议解析
        if (buffer.byteLength < 60) {
            throw new Error('Invalid Trojan buffer');
        }

        const crLfIndex = 56;
        if (bytes[crLfIndex] !== 0x0d || bytes[crLfIndex + 1] !== 0x0a) {
            throw new Error('Invalid Trojan header format');
        }

        let offset = crLfIndex + 2;
        const command = bytes[offset++];
        const addressType = bytes[offset++];
        let hostname = '';

        // 地址解析 (与 VLESS 类似，但地址类型映射不同)
        switch (addressType) {
            case 1: // IPv4
                hostname = `${bytes[offset]}.${bytes[offset + 1]}.${bytes[offset + 2]}.${bytes[offset + 3]}`;
                offset += 4;
                break;
            case 3: // Domain name (注意：Trojan 中域名是类型 3)
                const domainLength = bytes[offset++];
                hostname = decoder.decode(bytes.subarray(offset, offset + domainLength));
                offset += domainLength;
                break;
            case 4: // IPv6 (注意：Trojan 中 IPv6 是类型 4)
                const ipv6Parts = [];
                for (let i = 0; i < 16; i += 2) {
                    ipv6Parts.push(((bytes[offset + i] << 8) | bytes[offset + i + 1]).toString(16));
                }
                hostname = ipv6Parts.join(':');
                offset += 16;
                break;
            default:
                throw new Error(`Unsupported address type: ${addressType}`);
        }

        const port = view.getUint16(offset);
        offset += 2;
        offset += 2; // 跳过 CRLF
        const rawClientData = bytes.subarray(offset);

        return {
            addressType,
            addressRemote: hostname,
            portRemote: port,
            rawClientData
        };
    } else {
        throw new Error(`Unsupported protocol mode: ${protocolMode}`);
    }
}

// 辅助函数：将 UUID 字符串转换为字节数组
function uuidToBytes(uuid) {
    const hex = uuid.replace(/-/g, '');
    const bytes = new Uint8Array(16);
    for (let i = 0; i < 16; i++) {
        bytes[i] = parseInt(hex.substr(i * 2, 2), 16);
    }
    return bytes;
}

// 辅助函数：将 IPv6 字符串转换为字节数组
function ipv6ToBytes(ipv6) {
    const parts = ipv6.split(':');
    const bytes = new Uint8Array(16);
    for (let i = 0; i < 8; i++) {
        const part = parseInt(parts[i] || '0', 16);
        bytes[i * 2] = (part >> 8) & 0xff;
        bytes[i * 2 + 1] = part & 0xff;
    }
    return bytes;
}

// 辅助函数：将字符串转换为字节数组
function stringToBytes(str) {
    return new TextEncoder().encode(str);
}

// 创建测试用的 VLESS 协议 buffer
function createVLESSBuffer(addressType, address, port = 80) {
    const uuid = globalSessionConfig.user.id;
    const uuidBytes = uuidToBytes(uuid);
    
    const buffer = [];
    
    // Version (1 byte)
    buffer.push(0x00);
    
    // UUID (16 bytes)
    buffer.push(...uuidBytes);
    
    // Additional info length (1 byte) - 设为 0
    buffer.push(0x00);
    
    // Command (1 byte) - TCP
    buffer.push(0x01);
    
    // Port (2 bytes, big-endian)
    buffer.push((port >> 8) & 0xff);
    buffer.push(port & 0xff);
    
    // Address type (1 byte)
    buffer.push(addressType);
    
    // Address data
    switch (addressType) {
        case 1: // IPv4
            const ipParts = address.split('.').map(Number);
            buffer.push(...ipParts);
            break;
        case 2: // Domain
            const domainBytes = stringToBytes(address);
            buffer.push(domainBytes.length);
            buffer.push(...domainBytes);
            break;
        case 3: // IPv6
            const ipv6Bytes = ipv6ToBytes(address);
            buffer.push(...ipv6Bytes);
            break;
    }
    
    // Raw client data (可选)
    const clientData = stringToBytes('Hello World');
    buffer.push(...clientData);
    
    return new Uint8Array(buffer).buffer;
}

// 创建测试用的 Trojan 协议 buffer
function createTrojanBuffer(addressType, address, port = 80) {
    const buffer = [];
    
    // Password (56 bytes) - 使用固定的测试密码
    const password = 'a233255z'.padEnd(56, '\0');
    buffer.push(...stringToBytes(password));
    
    // CRLF (2 bytes)
    buffer.push(0x0d, 0x0a);
    
    // Command (1 byte) - TCP
    buffer.push(0x01);
    
    // Address type (1 byte) - 注意 Trojan 的地址类型映射
    const trojanAddressType = addressType === 2 ? 3 : (addressType === 3 ? 4 : addressType);
    buffer.push(trojanAddressType);
    
    // Address data
    switch (trojanAddressType) {
        case 1: // IPv4
            const ipParts = address.split('.').map(Number);
            buffer.push(...ipParts);
            break;
        case 3: // Domain (Trojan 中域名是类型 3)
            const domainBytes = stringToBytes(address);
            buffer.push(domainBytes.length);
            buffer.push(...domainBytes);
            break;
        case 4: // IPv6 (Trojan 中 IPv6 是类型 4)
            const ipv6Bytes = ipv6ToBytes(address);
            buffer.push(...ipv6Bytes);
            break;
    }
    
    // Port (2 bytes, big-endian)
    buffer.push((port >> 8) & 0xff);
    buffer.push(port & 0xff);
    
    // CRLF (2 bytes)
    buffer.push(0x0d, 0x0a);
    
    // Raw client data (可选)
    const clientData = stringToBytes('Hello World');
    buffer.push(...clientData);
    
    return new Uint8Array(buffer).buffer;
}

// 测试函数
function runTests() {
    console.log('=== parseProtocolHeader 测试开始 ===\n');
    
    const testCases = [
        // VLESS 测试用例
        {
            name: 'VLESS IPv4 测试',
            protocol: globalControllerConfig.targetProtocolType0,
            addressType: 1,
            address: '*******',
            port: 53,
            expected: {
                addressType: 1,
                addressRemote: '*******',
                portRemote: 53
            }
        },
        {
            name: 'VLESS Domain 测试',
            protocol: globalControllerConfig.targetProtocolType0,
            addressType: 2,
            address: 'google.com',
            port: 443,
            expected: {
                addressType: 2,
                addressRemote: 'google.com',
                portRemote: 443
            }
        },
        {
            name: 'VLESS IPv6 测试',
            protocol: globalControllerConfig.targetProtocolType0,
            addressType: 3,
            address: '2001:db8:85a3:0:0:8a2e:370:7334',
            port: 80,
            expected: {
                addressType: 3,
                addressRemote: '2001:db8:85a3:0:0:8a2e:370:7334',
                portRemote: 80
            }
        },
        
        // Trojan 测试用例
        {
            name: 'Trojan IPv4 测试',
            protocol: globalControllerConfig.targetProtocolType1,
            addressType: 1,
            address: '*******',
            port: 53,
            expected: {
                addressType: 1,
                addressRemote: '*******',
                portRemote: 53
            }
        },
        {
            name: 'Trojan Domain 测试',
            protocol: globalControllerConfig.targetProtocolType1,
            addressType: 2, // 输入时使用 2，但实际会转换为 3
            address: 'cloudflare.com',
            port: 443,
            expected: {
                addressType: 3, // Trojan 中域名是类型 3
                addressRemote: 'cloudflare.com',
                portRemote: 443
            }
        },
        {
            name: 'Trojan IPv6 测试',
            protocol: globalControllerConfig.targetProtocolType1,
            addressType: 3, // 输入时使用 3，但实际会转换为 4
            address: '2606:4700:4700:0:0:0:0:1111',
            port: 53,
            expected: {
                addressType: 4, // Trojan 中 IPv6 是类型 4
                addressRemote: '2606:4700:4700:0:0:0:0:1111',
                portRemote: 53
            }
        }
    ];
    
    let passedTests = 0;
    let totalTests = testCases.length;
    
    testCases.forEach((testCase, index) => {
        console.log(`测试 ${index + 1}: ${testCase.name}`);
        
        try {
            // 创建测试 buffer
            let buffer;
            if (testCase.protocol === globalControllerConfig.targetProtocolType0) {
                buffer = createVLESSBuffer(testCase.addressType, testCase.address, testCase.port);
            } else {
                buffer = createTrojanBuffer(testCase.addressType, testCase.address, testCase.port);
            }
            
            // 解析协议头
            const result = parseProtocolHeader(buffer, testCase.protocol);
            
            // 验证结果
            const isValid = 
                result.addressType === testCase.expected.addressType &&
                result.addressRemote === testCase.expected.addressRemote &&
                result.portRemote === testCase.expected.portRemote;
            
            if (isValid) {
                console.log(`✅ 通过`);
                console.log(`   地址类型: ${result.addressType}, 地址: ${result.addressRemote}, 端口: ${result.portRemote}`);
                passedTests++;
            } else {
                console.log(`❌ 失败`);
                console.log(`   期望: 类型=${testCase.expected.addressType}, 地址=${testCase.expected.addressRemote}, 端口=${testCase.expected.portRemote}`);
                console.log(`   实际: 类型=${result.addressType}, 地址=${result.addressRemote}, 端口=${result.portRemote}`);
            }
            
        } catch (error) {
            console.log(`❌ 异常: ${error.message}`);
        }
        
        console.log('');
    });
    
    console.log(`=== 测试完成 ===`);
    console.log(`通过: ${passedTests}/${totalTests}`);
    console.log(`成功率: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
}

// 运行测试
runTests();
